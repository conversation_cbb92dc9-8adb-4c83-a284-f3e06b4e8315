/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    Ble: typeof import('./src/components/Ble.vue')['default']
    BleDev: typeof import('./src/components/BleDev.vue')['default']
    NButton: typeof import('naive-ui')['NButton']
    NIcon: typeof import('naive-ui')['NIcon']
    NSwitch: typeof import('naive-ui')['NSwitch']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
