{"name": "ble-manage", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@mnlphlp/plugin-blec": "^0.4.1", "@tailwindcss/vite": "^4.1.12", "@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@iconify/json": "^2.2.378", "@iconify/tailwind4": "^1.0.6", "@tauri-apps/cli": "^2", "@vitejs/plugin-vue": "^5.2.1", "naive-ui": "^2.42.0", "tailwindcss": "^4.1.12", "typescript": "~5.6.2", "unplugin-auto-import": "^20.1.0", "unplugin-vue-components": "^29.0.0", "vite": "^6.0.3", "vue-tsc": "^2.1.10"}}