<template>
  <div class="flex flex-col h-screen bg-white rounded-2xl shadow-md">
    <!-- Header -->
    <div class="flex justify-between items-center p-4 border-b">
      <h1 class="flex items-center gap-2 text-lg font-semibold">
        <span class="icon-[mdi--bluetooth-audio] text-2xl text-blue-500"></span>
        <span class="text-blue-500">蓝牙管理</span>
      </h1>
      <div class="flex items-center gap-3">
        <n-button circle size="large">
          <template #icon>
            <div class="icon-[mdi--settings]"></div>
          </template>
        </n-button>
        <n-button circle size="large">
          <template #icon>
            <div class="icon-[mdi--refresh]"></div>
          </template>
        </n-button>

        <n-switch v-model:value="active" />
      </div>
    </div>

    <!-- Body -->
    <div class="flex flex-1 divide-x">
      <!-- Nearby Devices -->
      <div class="w-1/2 p-4">
        <h2 class="font-semibold mb-3">附近设备</h2>
        <div class="space-y-3">
          <div class="flex justify-between items-center p-3 border rounded-xl hover:bg-gray-50">
            <div>
              <p class="font-medium flex items-center gap-2">🎧 AirPods Pro <span class="text-green-600 text-sm">•
                  Connected</span></p>
            </div>
            <button class="px-3 py-1 border rounded-lg hover:bg-gray-100">Disconnect</button>
          </div>

          <div class="flex justify-between items-center p-3 border rounded-xl hover:bg-gray-50">
            <div>
              <p class="font-medium flex items-center gap-2">⌨️ Logitech MX Keys <span class="text-gray-500 text-sm">•
                  Paired</span></p>
            </div>
            <button class="px-3 py-1 border rounded-lg hover:bg-gray-100">Connect</button>
          </div>

          <div class="flex justify-between items-center p-3 border rounded-xl hover:bg-gray-50">
            <div>
              <p class="font-medium flex items-center gap-2">📱 Galaxy S23 <span class="text-gray-500 text-sm">•
                  Available</span></p>
            </div>
            <button class="px-3 py-1 border rounded-lg hover:bg-gray-100">Connect</button>
          </div>

          <div class="flex justify-between items-center p-3 border rounded-xl hover:bg-gray-50">
            <div>
              <p class="font-medium flex items-center gap-2">🎧 Jabra Elite 85h <span class="text-gray-500 text-sm">•
                  Paired</span></p>
            </div>
            <button class="px-3 py-1 border rounded-lg hover:bg-gray-100">Paired</button>
          </div>
        </div>
      </div>

      <!-- Device Details -->
      <div class="w-1/2 p-4">
        <h2 class="font-semibold mb-3">设备详情</h2>
        <div class="p-4 border rounded-xl">
          <div class="flex items-center gap-2 mb-4">
            <span class="text-2xl">🎧</span>
            <div>
              <p class="font-medium">AirPods Pro</p>
              <p class="text-green-600 text-sm">Connected</p>
            </div>
          </div>
          <p><span class="font-medium">状态:</span> Connected</p>
          <p><span class="font-medium">电量:</span> 80%</p>
          <p><span class="font-medium">服务:</span> Audio Sink, HFP</p>
          <div class="flex gap-2 mt-4">
            <button class="px-3 py-1 border rounded-lg hover:bg-gray-100">断开连接</button>
            <button class="px-3 py-1 border rounded-lg hover:bg-gray-100">忽略设备</button>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <div class="flex justify-between items-center p-3 border-t text-sm text-gray-600">
      <span>Adapter: Intel Wireless Bluetooth v5.3</span>
      <span>Connected: 2 devices</span>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const active = ref(false);

</script>

<style scoped></style>
